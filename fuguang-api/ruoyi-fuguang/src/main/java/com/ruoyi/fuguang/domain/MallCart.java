package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 购物车对象 mall_cart
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class MallCart extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 购物车ID */
    private Long cartId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 商品规格ID */
    @Excel(name = "商品规格ID")
    private Long specId;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Integer quantity;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 商品信息 */
    private MallProduct product;

    /** 商品规格信息 */
    private MallProductSpec productSpec;

    /** 小计金额 */
    private BigDecimal totalPrice;

    public void setCartId(Long cartId) 
    {
        this.cartId = cartId;
    }

    public Long getCartId() 
    {
        return cartId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId()
    {
        return productId;
    }
    public void setSpecId(Long specId)
    {
        this.specId = specId;
    }

    public Long getSpecId()
    {
        return specId;
    }
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    public Integer getQuantity() 
    {
        return quantity;
    }
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }
    public void setUpdateTime(Date updateTime) 
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() 
    {
        return updateTime;
    }

    public MallProduct getProduct() 
    {
        return product;
    }

    public void setProduct(MallProduct product)
    {
        this.product = product;
    }

    public MallProductSpec getProductSpec()
    {
        return productSpec;
    }

    public void setProductSpec(MallProductSpec productSpec)
    {
        this.productSpec = productSpec;
    }

    public BigDecimal getTotalPrice()
    {
        if (productSpec != null && quantity != null) {
            return productSpec.getSalePrice().multiply(new BigDecimal(quantity));
        } else if (product != null && quantity != null) {
            return product.getSalePrice().multiply(new BigDecimal(quantity));
        }
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) 
    {
        this.totalPrice = totalPrice;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cartId", getCartId())
            .append("userId", getUserId())
            .append("productId", getProductId())
            .append("specId", getSpecId())
            .append("quantity", getQuantity())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
